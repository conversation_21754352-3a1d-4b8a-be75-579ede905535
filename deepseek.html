<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat Interface</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.2/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Seguro UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 600px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(90deg, #4f46e5, #7c3aed);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .chat-header .status {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .config-section {
            padding: 15px 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .config-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .config-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .config-group label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .config-group input, .config-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .config-group input:focus, .config-group select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .thinking-section {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 12px;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .thinking-header {
            padding: 12px 16px;
            background: #e0f2fe;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #0369a1;
            transition: background-color 0.2s;
        }

        .thinking-header:hover {
            background: #b3e5fc;
        }

        .thinking-toggle {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .thinking-toggle.expanded {
            transform: rotate(90deg);
        }

        .thinking-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .thinking-content.expanded {
            max-height: 300px;
        }

        .thinking-text {
            padding: 12px 16px;
            font-size: 13px;
            line-height: 1.5;
            color: #0f172a;
            background: #f8fafc;
            font-family: 'Monaco', 'Menlo', monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }

        .message.assistant {
            align-self: flex-start;
            background: #f1f5f9;
            color: #334155;
            border: 1px solid #e2e8f0;
            padding: 0;
        }

        .message.assistant .message-content {
            padding: 12px 16px;
        }

        .message.assistant h1,
        .message.assistant h2,
        .message.assistant h3,
        .message.assistant h4,
        .message.assistant h5,
        .message.assistant h6 {
            margin: 16px 0 8px 0;
            color: #1e293b;
            font-weight: 600;
        }

        .message.assistant h1 { font-size: 1.5em; }
        .message.assistant h2 { font-size: 1.3em; }
        .message.assistant h3 { font-size: 1.1em; }

        .message.assistant p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .message.assistant ul,
        .message.assistant ol {
            margin: 8px 0;
            padding-left: 24px;
        }

        .message.assistant li {
            margin: 4px 0;
            line-height: 1.5;
        }

        .message.assistant blockquote {
            margin: 12px 0;
            padding: 8px 16px;
            border-left: 4px solid #e2e8f0;
            background: #f8fafc;
            font-style: italic;
        }

        .message.assistant code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 0.9em;
            color: #dc2626;
        }

        .message.assistant pre {
            margin: 12px 0;
            background: #1e293b;
            border-radius: 8px;
            overflow-x: auto;
        }

        .message.assistant pre code {
            display: block;
            padding: 16px;
            background: transparent;
            color: #e2e8f0;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .message.assistant table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0;
            font-size: 0.9em;
        }

        .message.assistant th,
        .message.assistant td {
            border: 1px solid #e2e8f0;
            padding: 8px 12px;
            text-align: left;
        }

        .message.assistant th {
            background: #f8fafc;
            font-weight: 600;
        }

        .message.assistant a {
            color: #4f46e5;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.2s;
        }

        .message.assistant a:hover {
            border-bottom-color: #4f46e5;
        }

        .message.assistant strong {
            font-weight: 600;
            color: #1e293b;
        }

        .message.assistant em {
            font-style: italic;
            color: #64748b;
        }

        .message.assistant hr {
            margin: 20px 0;
            border: none;
            border-top: 1px solid #e2e8f0;
        }

        .message.system {
            align-self: center;
            background: #fef3c7;
            color: #92400e;
            font-size: 14px;
            border: 1px solid #fbbf24;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 50px;
            max-height: 120px;
            padding: 15px 50px 15px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            transition: border-color 0.2s;
            font-family: inherit;
        }

        .message-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .send-button {
            position: absolute;
            right: 8px;
            bottom: 8px;
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #64748b;
            font-style: italic;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            background: #64748b;
            border-radius: 50%;
            animation: loadingDot 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loadingDot {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .error-message {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 20px;
            font-size: 14px;
        }

        .clear-button {
            padding: 12px 20px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .clear-button:hover {
            background: #4b5563;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
            }

            .config-row {
                flex-direction: column;
                align-items: stretch;
            }

            .message {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>DeepSeek Chat Interface</h1>
            <div class="status">
                <div class="status-dot"></div>
                <span>在线</span>
            </div>
        </div>

        <div class="config-section">
            <div class="config-row">
                <div class="config-group">
                    <label>API Key</label>
                    <input type="password" id="apiKey" placeholder="sk-..." value="sk-53e2912129c3460b8b8b939cebbf818a">
                </div>
                <div class="config-group">
                    <label>Base URL</label>
                    <input type="text" id="baseUrl" placeholder="https://api.deepseek.com/v1" value="https://api.deepseek.com/v1">
                </div>
                <div class="config-group">
                    <label>模型</label>
                    <select id="model">
                        <option value="deepseek-chat">deepseek-chat</option>
                        <option value="deepseek-coder">deepseek-coder</option>
                    </select>
                </div>
            </div>
            <div class="config-row" style="margin-top: 10px;">
                <div class="config-group" style="flex: 1;">
                    <label>系统提示词</label>
                    <input type="text" id="systemPrompt" placeholder="你是一个有用的AI助手" value="你是一个有用的AI助手">
                </div>
            </div>
        </div>

        <div class="messages-container" id="messagesContainer">
            <div class="message system">
                欢迎使用 DeepSeek Chat Interface！请在下方输入您的消息开始对话。
            </div>
        </div>

        <div class="input-container">
            <div class="input-wrapper">
                <textarea
                    id="messageInput"
                    class="message-input"
                    placeholder="输入您的消息..."
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <button id="clearButton" class="clear-button">清空对话</button>
        </div>
    </div>

    <script>
        class DeepSeekChat {
            constructor() {
                this.messages = [];
                this.isLoading = false;
                if (this.initializeElements()) {
                    this.bindEvents();
                    this.setupMarked();
                } else {
                    console.error('Failed to initialize elements');
                }
            }

            setupMarked() {
                // 配置marked.js
                marked.setOptions({
                    highlight: function(code, lang) {
                        if (lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (err) {}
                        }
                        return hljs.highlightAuto(code).value;
                    },
                    breaks: true,
                    gfm: true
                });
            }

            initializeElements() {
                this.messagesContainer = document.getElementById('messagesContainer');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.clearButton = document.getElementById('clearButton');
                this.apiKeyInput = document.getElementById('apiKey');
                this.baseUrlInput = document.getElementById('baseUrl');
                this.modelSelect = document.getElementById('model');
                this.systemPromptInput = document.getElementById('systemPrompt');

                // 检查所有必需元素是否存在
                if (!this.messagesContainer || !this.messageInput || !this.sendButton ||
                    !this.clearButton || !this.apiKeyInput || !this.baseUrlInput ||
                    !this.modelSelect || !this.systemPromptInput) {
                    console.error('Some required elements are missing');
                    return false;
                }
                return true;
            }

            bindEvents() {
                if (!this.sendButton || !this.clearButton || !this.messageInput) {
                    console.error('Cannot bind events - elements not found');
                    return;
                }

                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.clearButton.addEventListener('click', () => this.clearMessages());
                this.messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                this.messageInput.addEventListener('input', () => this.autoResize());
            }

            autoResize() {
                this.messageInput.style.height = 'auto';
                this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
            }

            addMessage(role, content, thinking = null) {
                const messageWrapper = document.createElement('div');

                // 如果有thinking内容，先添加thinking部分
                if (thinking && role === 'assistant') {
                    const thinkingDiv = document.createElement('div');
                    thinkingDiv.className = 'thinking-section';
                    thinkingDiv.innerHTML = `
                        <div class="thinking-header" onclick="this.parentElement.querySelector('.thinking-content').classList.toggle('expanded'); this.querySelector('.thinking-toggle').classList.toggle('expanded')">
                            <div class="thinking-toggle">▶</div>
                            <span>🤔 AI思考过程</span>
                        </div>
                        <div class="thinking-content">
                            <div class="thinking-text">${thinking}</div>
                        </div>
                    `;
                    messageWrapper.appendChild(thinkingDiv);
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;

                if (role === 'assistant') {
                    // 对助手回复使用Markdown渲染
                    const contentDiv = document.createElement('div');
                    contentDiv.className = 'message-content';
                    contentDiv.innerHTML = marked.parse(content);
                    messageDiv.appendChild(contentDiv);
                } else {
                    // 用户消息保持纯文本
                    messageDiv.textContent = content;
                }

                messageWrapper.appendChild(messageDiv);

                this.messagesContainer.appendChild(messageWrapper);
                this.scrollToBottom();
            }

            addLoadingMessage() {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message assistant loading';
                loadingDiv.innerHTML = `
                    <span>AI正在思考</span>
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                `;
                loadingDiv.id = 'loadingMessage';
                this.messagesContainer.appendChild(loadingDiv);
                this.scrollToBottom();
                return loadingDiv;
            }

            removeLoadingMessage() {
                const loadingMessage = document.getElementById('loadingMessage');
                if (loadingMessage) {
                    loadingMessage.remove();
                }
            }

            showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = `错误: ${message}`;
                this.messagesContainer.appendChild(errorDiv);
                this.scrollToBottom();
            }

            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }

            async sendMessage() {
                const userMessage = this.messageInput.value.trim();
                if (!userMessage || this.isLoading) return;

                const apiKey = this.apiKeyInput.value.trim();
                const baseUrl = this.baseUrlInput.value.trim();
                const model = this.modelSelect.value;
                const systemPrompt = this.systemPromptInput.value.trim();

                if (!apiKey) {
                    this.showError('请输入API Key');
                    return;
                }

                this.addMessage('user', userMessage);
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';

                this.isLoading = true;
                this.sendButton.disabled = true;
                const loadingMessage = this.addLoadingMessage();

                // 构建消息数组
                const messages = [
                    {"role": "system", "content": systemPrompt},
                    ...this.messages,
                    {"role": "user", "content": userMessage}
                ];

                try {
                    const response = await fetch(`${baseUrl}/chat/completions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: JSON.stringify({
                            model: model,
                            messages: messages,
                            stream: false
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    const assistantMessage = data.choices[0].message.content;

                    this.removeLoadingMessage();
                    this.addMessage('assistant', assistantMessage);

                    // 保存对话历史
                    this.messages.push(
                        {"role": "user", "content": userMessage},
                        {"role": "assistant", "content": assistantMessage}
                    );

                } catch (error) {
                    this.removeLoadingMessage();
                    this.showError(`请求失败: ${error.message}`);
                } finally {
                    this.isLoading = false;
                    this.sendButton.disabled = false;
                    this.messageInput.focus();
                }
            }

            clearMessages() {
                this.messagesContainer.innerHTML = `
                    <div class="message system">
                        对话已清空。请输入新的消息开始对话。
                    </div>
                `;
                this.messages = [];
            }
        }

        // 初始化聊天界面
        document.addEventListener('DOMContentLoaded', () => {
            const chat = new DeepSeekChat();

            // 确保页面加载完成后聚焦输入框
            setTimeout(() => {
                chat.messageInput.focus();
            }, 100);
        });
    </script>
</body>
</html>